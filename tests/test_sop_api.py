"""SOP API测试"""
import pytest
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import AsyncMock, patch

from app.main import app

client = TestClient(app)


def test_health_check():
    """测试健康检查端点"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data
    assert "environment" in data


def test_root_endpoint():
    """测试根端点"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "docs" in data
    assert "health" in data


@patch('app.services.sop_service.SOPService.process_upload')
async def test_sop_upload_success(mock_process_upload):
    """测试SOP文件上传成功"""
    # 模拟成功响应
    mock_process_upload.return_value = {
        "success": True,
        "data": {"工序": []},
        "message": "处理成功"
    }
    
    # 创建测试文件
    test_file = ("test.xlsx", b"fake excel content", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    
    response = client.post(
        "/api/v1/sop/upload",
        files={"file": test_file},
        data={"max_len": "40"}
    )
    
    assert response.status_code == 200


def test_sop_upload_invalid_file():
    """测试上传无效文件"""
    # 创建无效文件
    test_file = ("test.txt", b"not excel content", "text/plain")
    
    response = client.post(
        "/api/v1/sop/upload",
        files={"file": test_file},
        data={"max_len": "40"}
    )
    
    # 应该返回验证错误
    assert response.status_code in [400, 422]


def test_sop_upload_invalid_max_len():
    """测试无效的max_len参数"""
    test_file = ("test.xlsx", b"fake excel content", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
    
    response = client.post(
        "/api/v1/sop/upload",
        files={"file": test_file},
        data={"max_len": "0"}  # 无效值
    )
    
    assert response.status_code == 400


def test_api_documentation():
    """测试API文档可访问性"""
    response = client.get("/docs")
    assert response.status_code == 200
    
    response = client.get("/openapi.json")
    assert response.status_code == 200
