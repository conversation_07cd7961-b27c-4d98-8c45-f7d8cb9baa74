[project]
name = "ai-server"
version = "0.1.0"
description = ""
authors = [
    {name = "<PERSON>",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "fastapi[standard] (>=0.115.12,<0.116.0)",
    "pandas (>=2.3.0,<3.0.0)",
    "openpyxl (>=3.1.5,<4.0.0)",
    "requests (>=2.32.4,<3.0.0)",
    "pydantic-settings (>=2.9.1,<3.0.0)",
    "black (>=25.1.0,<26.0.0)",
    "isort (>=6.0.1,<7.0.0)",
    "uvicorn[standard] (>=0.34.3,<0.35.0)",
    "aiohttp (>=3.12.13,<4.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"


[[tool.poetry.source]]
name = "tsinghua"
url = "https://pypi.tuna.tsinghua.edu.cn/simple/"
priority = "primary"

[tool.poetry.group.dev.dependencies]
pytest = "^8.4.1"
pytest-asyncio = "^1.0.0"
httpx = "^0.28.1"

