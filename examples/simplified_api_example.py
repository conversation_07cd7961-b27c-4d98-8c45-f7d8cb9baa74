"""
简化后的API使用示例
演示Form参数的正确使用和直接数据返回
"""

import asyncio

import aiohttp


async def test_health_check():
    """测试健康检查接口"""
    async with aiohttp.ClientSession() as session:
        async with session.get("http://localhost:30101/health") as response:
            data = await response.json()
            print("✅ 健康检查:")
            print(f"   状态: {data['status']}")
            print(f"   版本: {data['version']}")
            print(f"   环境: {data['environment']}")
            return response.status == 200


async def test_form_data_upload():
    """测试Form参数上传"""
    # 创建form-data
    data = aiohttp.FormData()
    data.add_field(
        "file",
        b"fake excel content",
        filename="test.xlsx",
        content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    )
    data.add_field("max_len", "50")  # Form参数

    async with aiohttp.ClientSession() as session:
        async with session.post(
            "http://localhost:30101/api/v1/sop/upload", data=data
        ) as response:
            result = await response.json()
            print(f"\n✅ Form参数上传测试 (状态码: {response.status}):")
            if response.status == 400:
                print(f"   预期错误: {result['detail']}")
                return True
            else:
                print(f"   结果: {result}")
                return response.status == 200


async def test_max_len_validation():
    """测试max_len参数验证"""
    data = aiohttp.FormData()
    data.add_field("file", b"fake content", filename="test.xlsx")
    data.add_field("max_len", "0")  # 无效值

    async with aiohttp.ClientSession() as session:
        async with session.post(
            "http://localhost:30101/api/v1/sop/upload", data=data
        ) as response:
            result = await response.json()
            print(f"\n✅ 参数验证测试 (状态码: {response.status}):")
            print(f"   验证消息: {result['detail']}")
            return (
                response.status == 400 and "max_len必须在1-1000之间" in result["detail"]
            )


async def test_file_type_validation():
    """测试文件类型验证"""
    data = aiohttp.FormData()
    data.add_field("file", b"not excel", filename="test.txt")  # 错误文件类型
    data.add_field("max_len", "40")

    async with aiohttp.ClientSession() as session:
        async with session.post(
            "http://localhost:30101/api/v1/sop/upload", data=data
        ) as response:
            result = await response.json()
            print(f"\n✅ 文件类型验证测试 (状态码: {response.status}):")
            print(f"   验证消息: {result['detail']}")
            return response.status == 400 and "不支持的文件格式" in result["detail"]


async def main():
    """主测试函数"""
    print("🚀 测试简化后的FastAPI接口...")
    print("=" * 60)

    # 测试健康检查
    health_ok = await test_health_check()

    # 测试Form参数
    form_ok = await test_form_data_upload()

    # 测试参数验证
    validation_ok = await test_max_len_validation()

    # 测试文件类型验证
    file_type_ok = await test_file_type_validation()

    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"✅ 健康检查: {'通过' if health_ok else '失败'}")
    print(f"✅ Form参数接收: {'通过' if form_ok else '失败'}")
    print(f"✅ 参数验证: {'通过' if validation_ok else '失败'}")
    print(f"✅ 文件类型验证: {'通过' if file_type_ok else '失败'}")

    all_passed = all([health_ok, form_ok, validation_ok, file_type_ok])
    print(f"\n🎉 总体结果: {'所有测试通过' if all_passed else '部分测试失败'}")

    print("\n📝 简化优化要点:")
    print("1. ✅ 使用 Form(40) 正确接收 form-data 参数")
    print("2. ✅ 直接返回数据，无需包装 success/message/data")
    print("3. ✅ 在服务层统一验证，避免路由层重复验证")
    print("4. ✅ 删除过度设计的schemas和依赖注入")
    print("5. ✅ 保留核心功能，提升代码可读性和维护性")


if __name__ == "__main__":
    print("请确保FastAPI服务器正在运行在 http://localhost:30101")
    print("启动命令: poetry run python -m app.main")
    print()

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {e}")
