import requests

from app.core.config import settings


def upload_to_dify(file_name: str, file_bytes: bytes):
    upload_url = f"{settings.DIFY_API_URL}/v1/files/upload"
    headers = {
        "Authorization": f"Bearer {settings.DIFY_API_KEY}",
    }

    files = {
        "file": (
            file_name,
            file_bytes,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
    }
    data = {
        "user": settings.DIFY_USER,
    }

    response = requests.post(upload_url, headers=headers, data=data, files=files)
    if response.status_code != 201:
        raise Exception(f"Dify 上传失败: {response.text}")
    return response.json().get("id")
