import io
import os

import pandas as pd


def excel_divider_stream(file_bytes: bytes, M: int = 40):
    # 读取表头和数据
    header_rows = pd.read_excel(io.BytesIO(file_bytes), header=None, nrows=6, dtype=str)
    df = pd.read_excel(io.BytesIO(file_bytes), header=5, dtype=str)

    df.columns = df.columns.str.strip().str.replace("\n", "")
    df["工序编号"] = df["工序编号"].ffill()

    group_sizes = df.groupby("工序编号").size().to_dict()

    # 分块逻辑
    chunks = []
    current_ops = []
    current_count = 0
    for op, rows in group_sizes.items():
        if current_count + rows > M:
            chunks.append((current_ops, current_count))
            current_ops = []
            current_count = 0
        current_ops.append(op)
        current_count += rows
    if current_ops:
        chunks.append((current_ops, current_count))

    # 生成每个分块的 Excel 文件流
    file_streams = []
    start_idx = 0
    for idx, (ops, total_rows) in enumerate(chunks, start=1):
        block_df = df.iloc[start_idx : start_idx + total_rows].copy()
        start_idx += total_rows

        stream = io.BytesIO()
        with pd.ExcelWriter(stream, engine="openpyxl") as writer:
            header_rows.to_excel(writer, index=False, header=False, sheet_name="Sheet1")
            block_df.to_excel(
                writer, index=False, header=False, startrow=6, sheet_name="Sheet1"
            )
        stream.seek(0)
        file_streams.append((f"chunk_{idx}.xlsx", stream))

    return file_streams
