import requests

from app.core.config import settings
from app.utils.json_parser import <PERSON><PERSON><PERSON>arser

parser = <PERSON>SONParser()


def run_dify_workflow(file_id: str):
    workflow_url = f"{settings.DIFY_API_URL}/v1/workflows/run"
    headers = {
        "Authorization": f"Bearer {settings.DIFY_API_KEY}",
    }

    payload = {
        "inputs": {
            "planning_file": {
                "type": "document",
                "transfer_method": "local_file",
                "upload_file_id": file_id,
            }
        },
        "response_mode": "blocking",
        "user": settings.DIFY_USER,
    }

    response = requests.post(workflow_url, headers=headers, json=payload)
    if response.status_code != 200:
        raise Exception(f"Dify 工作流调用失败: {response.text}")

    result = response.json()
    if result["data"]["status"] == "succeeded":
        output = result["data"]["outputs"]["result"]
        try:
            parsed = parser.parse(output)
            return {"data": parsed, "status": 1}
        except Exception:
            return {"data": output.strip(), "status": 0}
    else:
        return {"data": f"Dify 工作流失败: {result['data'].get('error')}", "status": 0}
