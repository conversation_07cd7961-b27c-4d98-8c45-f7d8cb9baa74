"""SOP相关的API路由"""
from fastapi import APIRouter, Depends, File, HTTPException, UploadFile

from app.dependencies.services import get_sop_service
from app.schemas.sop import ErrorResponse, SOPUploadResponse
from app.services.sop_service import SOPService

router = APIRouter()


@router.post(
    "/upload",
    response_model=SOPUploadResponse,
    responses={
        400: {"model": ErrorResponse, "description": "请求参数错误"},
        500: {"model": ErrorResponse, "description": "服务器内部错误"},
    },
    summary="上传SOP文件",
    description="上传Excel格式的SOP文件，系统会自动分割并处理文件内容"
)
async def upload_sop_file(
    file: UploadFile = File(..., description="要上传的Excel文件"),
    max_len: int = 40,
    sop_service: SOPService = Depends(get_sop_service)
) -> SOPUploadResponse:
    """
    上传并处理SOP文件

    - **file**: Excel格式的SOP文件
    - **max_len**: 每个分块的最大行数（默认40）
    """
    try:
        # 验证max_len参数
        if max_len <= 0 or max_len > 1000:
            raise HTTPException(
                status_code=400,
                detail="max_len必须在1-1000之间"
            )

        # 处理文件上传
        result = await sop_service.process_upload(file, max_len)
        return result

    except HTTPException:
        # 重新抛出HTTP异常
        raise
    except Exception as e:
        # 记录错误并返回通用错误响应
        raise HTTPException(
            status_code=500,
            detail=f"处理文件时发生未知错误: {str(e)}"
        )
