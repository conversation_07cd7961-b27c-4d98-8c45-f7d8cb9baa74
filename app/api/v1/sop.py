from fastapi import APIRouter, File, Form, UploadFile
from starlette.responses import JSONResponse

from app.utils.excel_divider_stream import excel_divider_stream
from app.utils.run_dify_workflow import run_dify_workflow
from app.utils.upload_to_dify import upload_to_dify

router = APIRouter()


@router.post("/upload")
async def upload(file: UploadFile = File(...), max_len: str = Form("40")):
    try:
        contents = await file.read()
        file_streams = excel_divider_stream(contents, int(max_len))

        results = []
        for filename, stream in file_streams:
            file_id = upload_to_dify(filename, stream.getvalue())
            result = run_dify_workflow(file_id)

            if result["status"] == 1:
                results.append(result["data"])

        processes = []
        merged = {"工序": []}
        for result in results:
            processes = processes + result["工序"]
            for k, v in result.items():
                if k not in merged and k != "工序":
                    merged[k] = v

        merged["工序"] = processes

        return JSONResponse(content=merged)
    except Exception as e:
        return JSONResponse(content={"error": str(e)}, status_code=500)
