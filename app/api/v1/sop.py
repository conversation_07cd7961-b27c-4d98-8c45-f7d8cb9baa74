"""SOP相关的API路由"""

from typing import Any, Dict

from fastapi import APIRouter, File, Form, UploadFile

from app.services.dify_service import DifyService
from app.services.sop_service import SOPService

router = APIRouter()


@router.post("/upload", summary="上传SOP文件")
async def upload_sop_file(
    file: UploadFile = File(...), max_len: int = Form(40)
) -> Dict[str, Any]:
    """上传Excel格式的SOP文件，系统会自动分割并处理文件内容"""
    # 创建服务实例
    dify_service = DifyService()
    sop_service = SOPService(dify_service)

    return await sop_service.process_upload(file, max_len)
