"""服务依赖注入"""
from functools import lru_cache

from app.services.dify_service import DifyService
from app.services.sop_service import SOPService


@lru_cache()
def get_dify_service() -> DifyService:
    """获取Dify服务实例（单例模式）"""
    return DifyService()


def get_sop_service(
    dify_service: DifyService = None
) -> SOPService:
    """获取SOP服务实例"""
    if dify_service is None:
        dify_service = get_dify_service()
    return SOPService(dify_service)
