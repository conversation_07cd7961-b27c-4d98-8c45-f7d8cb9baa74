"""通用依赖注入"""
import time
from typing import Optional

from fastapi import Depends, HTTPException, Request

from app.core.config import settings
from app.schemas.common import PaginationParams


def get_request_id(request: Request) -> str:
    """获取请求ID"""
    return getattr(request.state, "request_id", "unknown")


def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    # 优先从代理头获取真实IP
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()
    
    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip
    
    # 回退到直接连接IP
    return request.client.host if request.client else "unknown"


def get_pagination_params(
    page: int = 1,
    size: int = 20
) -> PaginationParams:
    """获取分页参数"""
    return PaginationParams(page=page, size=size)


def verify_api_key(api_key: Optional[str] = None) -> bool:
    """验证API密钥（如果需要的话）"""
    # 这里可以实现API密钥验证逻辑
    # 目前返回True，表示不需要验证
    return True


def rate_limit_check(request: Request) -> bool:
    """简单的速率限制检查"""
    # 这里可以实现基于IP或用户的速率限制
    # 目前返回True，表示不限制
    return True


class CommonDependencies:
    """通用依赖集合类"""
    
    def __init__(
        self,
        request: Request,
        request_id: str = Depends(get_request_id),
        client_ip: str = Depends(get_client_ip)
    ):
        self.request = request
        self.request_id = request_id
        self.client_ip = client_ip
        self.timestamp = time.time()


def get_common_deps(
    request: Request,
    request_id: str = Depends(get_request_id),
    client_ip: str = Depends(get_client_ip)
) -> CommonDependencies:
    """获取通用依赖"""
    return CommonDependencies(request, request_id, client_ip)
