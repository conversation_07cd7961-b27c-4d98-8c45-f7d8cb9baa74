from typing import Any, Dict, Optional


class BaseAPIException(Exception):
    """API基础异常类"""

    def __init__(
        self,
        message: str,
        status_code: int = 500,
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationException(BaseAPIException):
    """数据验证异常"""

    def __init__(
        self, message: str = "数据验证失败", details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            status_code=400,
            error_code="VALIDATION_ERROR",
            details=details,
        )


class FileProcessingException(BaseAPIException):
    """文件处理异常"""

    def __init__(
        self, message: str = "文件处理失败", details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            status_code=400,
            error_code="FILE_PROCESSING_ERROR",
            details=details,
        )


class ExternalServiceException(BaseAPIException):
    """外部服务调用异常"""

    def __init__(
        self,
        message: str = "外部服务调用失败",
        service_name: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ):
        if service_name:
            message = f"{service_name}服务调用失败: {message}"

        super().__init__(
            message=message,
            status_code=502,
            error_code="EXTERNAL_SERVICE_ERROR",
            details=details,
        )


class DifyServiceException(ExternalServiceException):
    """Dify服务异常"""

    def __init__(
        self,
        message: str = "Dify服务调用失败",
        details: Optional[Dict[str, Any]] = None,
    ):
        super().__init__(message=message, service_name="Dify", details=details)


class ConfigurationException(BaseAPIException):
    """配置异常"""

    def __init__(
        self, message: str = "配置错误", details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(
            message=message,
            status_code=500,
            error_code="CONFIGURATION_ERROR",
            details=details,
        )
