from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    ENVIRONMENT: str = Field(default="development", description="运行环境")
    DEBUG: bool = Field(default=True, description="调试模式")

    DIFY_API_KEY: str = Field(default="", description="Dify API 密钥")
    DIFY_API_URL: str = Field(default="", description="Dify API 地址")
    DIFY_USER: str = Field(default="admin", description="Dify 用户")

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        env_prefix = ""
        extra = "ignore"


settings = Settings()
