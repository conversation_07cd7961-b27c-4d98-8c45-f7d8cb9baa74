import logging
from typing import Literal, Optional

from pydantic import Field, field_validator, model_validator
from pydantic_settings import BaseSettings

from app.core.exceptions import ConfigurationException


class Settings(BaseSettings):
    """应用配置类"""

    # 基础配置
    ENVIRONMENT: Literal["development", "testing", "production"] = Field(
        default="development",
        description="运行环境"
    )
    DEBUG: bool = Field(default=True, description="调试模式")
    VERSION: str = Field(default="1.0.0", description="应用版本")

    # 服务配置
    HOST: str = Field(default="0.0.0.0", description="服务主机")
    PORT: int = Field(default=30101, ge=1, le=65535, description="服务端口")

    # 日志配置
    LOG_LEVEL: str = Field(default="INFO", description="日志级别")
    LOG_FILE_MAX_SIZE: int = Field(default=10 * 1024 * 1024, description="日志文件最大大小(字节)")
    LOG_FILE_BACKUP_COUNT: int = Field(default=5, description="日志文件备份数量")

    # Dify配置
    DIFY_API_KEY: str = Field(default="", description="Dify API 密钥")
    DIFY_API_URL: str = Field(default="", description="Dify API 地址")
    DIFY_USER: str = Field(default="admin", description="Dify 用户")
    DIFY_TIMEOUT: int = Field(default=300, ge=1, description="Dify API 超时时间(秒)")

    # 文件处理配置
    MAX_FILE_SIZE: int = Field(default=50 * 1024 * 1024, description="最大文件大小(字节)")
    ALLOWED_FILE_EXTENSIONS: list[str] = Field(
        default=[".xlsx", ".xls"],
        description="允许的文件扩展名"
    )
    DEFAULT_MAX_CHUNK_SIZE: int = Field(default=40, ge=1, le=1000, description="默认分块大小")

    # 性能配置
    MAX_CONCURRENT_REQUESTS: int = Field(default=10, ge=1, description="最大并发请求数")
    REQUEST_TIMEOUT: int = Field(default=300, ge=1, description="请求超时时间(秒)")

    @field_validator('LOG_LEVEL')
    @classmethod
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ConfigurationException(f"LOG_LEVEL必须是以下值之一: {', '.join(valid_levels)}")
        return v.upper()

    @field_validator('DIFY_API_URL')
    @classmethod
    def validate_dify_api_url(cls, v):
        """验证Dify API地址"""
        if v and not (v.startswith('http://') or v.startswith('https://')):
            raise ConfigurationException("DIFY_API_URL必须以http://或https://开头")
        return v

    @model_validator(mode='after')
    def validate_environment_settings(self):
        """验证环境相关配置"""
        if self.ENVIRONMENT == 'production' and self.DEBUG:
            logging.warning("生产环境不建议开启调试模式")

        if self.ENVIRONMENT != 'development':
            if not self.DIFY_API_KEY:
                raise ConfigurationException("非开发环境必须配置DIFY_API_KEY")
            if not self.DIFY_API_URL:
                raise ConfigurationException("非开发环境必须配置DIFY_API_URL")

        return self

    @property
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.ENVIRONMENT == "development"

    @property
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.ENVIRONMENT == "production"

    @property
    def is_testing(self) -> bool:
        """是否为测试环境"""
        return self.ENVIRONMENT == "testing"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        env_prefix = ""
        extra = "ignore"


def get_settings() -> Settings:
    """获取配置实例"""
    try:
        return Settings()
    except Exception as e:
        raise ConfigurationException(f"配置加载失败: {str(e)}")


# 全局配置实例
settings = get_settings()
