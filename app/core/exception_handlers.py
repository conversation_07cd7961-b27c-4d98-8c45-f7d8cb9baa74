import logging
from typing import Union

from fastapi import HTTP<PERSON>x<PERSON>, Request, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from pydantic import ValidationError

from app.core.exceptions import BaseAPIException
from app.schemas.common import ValidationErrorResponse
from app.schemas.sop import ErrorResponse

logger = logging.getLogger(__name__)


async def base_api_exception_handler(
    request: Request, exc: BaseAPIException
) -> JSONResponse:
    """处理自定义API异常"""
    logger.error(
        f"API异常 - 路径: {request.url.path}, "
        f"方法: {request.method}, "
        f"错误: {exc.message}, "
        f"状态码: {exc.status_code}"
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            success=False,
            error=exc.message,
            error_code=exc.error_code,
            details=exc.details,
        ).model_dump(),
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """处理HTTP异常"""
    logger.error(
        f"HTTP异常 - 路径: {request.url.path}, "
        f"方法: {request.method}, "
        f"状态码: {exc.status_code}, "
        f"详情: {exc.detail}"
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            success=False, error=str(exc.detail), error_code=f"HTTP_{exc.status_code}"
        ).model_dump(),
    )


async def validation_exception_handler(
    request: Request, exc: Union[RequestValidationError, ValidationError]
) -> JSONResponse:
    """处理数据验证异常"""
    logger.error(
        f"验证异常 - 路径: {request.url.path}, "
        f"方法: {request.method}, "
        f"错误: {exc.errors()}"
    )

    errors = []
    for error in exc.errors():
        field = ".".join(str(loc) for loc in error["loc"])
        errors.append({"field": field, "message": error["msg"], "code": error["type"]})

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=ValidationErrorResponse(
            success=False, message="请求数据验证失败", errors=errors
        ).model_dump(),
    )


async def general_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """处理通用异常"""
    logger.error(
        f"未处理异常 - 路径: {request.url.path}, "
        f"方法: {request.method}, "
        f"异常类型: {type(exc).__name__}, "
        f"异常信息: {str(exc)}",
        exc_info=True,
    )

    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content=ErrorResponse(
            success=False,
            error="服务器内部错误",
            error_code="INTERNAL_SERVER_ERROR",
            details={"exception_type": type(exc).__name__},
        ).model_dump(),
    )
