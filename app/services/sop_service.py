"""SOP处理相关的业务逻辑"""
import logging
from typing import Any, Dict, List, Tuple

from fastapi import HTTPException, UploadFile

from app.services.dify_service import DifyService
from app.utils.excel_divider_stream import excel_divider_stream

logger = logging.getLogger(__name__)


class SOPService:
    """SOP处理服务类"""
    
    def __init__(self, dify_service: DifyService):
        self.dify_service = dify_service
    
    def _validate_file(self, file: UploadFile) -> None:
        """验证上传的文件"""
        if not file.filename:
            raise HTTPException(status_code=400, detail="文件名不能为空")
        
        # 检查文件扩展名
        allowed_extensions = {'.xlsx', '.xls'}
        file_extension = file.filename.lower().split('.')[-1]
        if f'.{file_extension}' not in allowed_extensions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件格式。支持的格式: {', '.join(allowed_extensions)}"
            )
    
    def _process_file_streams(self, file_bytes: bytes, max_len: int) -> List[Tuple[str, bytes]]:
        """处理文件流，分割Excel文件"""
        try:
            file_streams = excel_divider_stream(file_bytes, max_len)
            # 转换为字节数据
            processed_streams = []
            for filename, stream in file_streams:
                processed_streams.append((filename, stream.getvalue()))
            return processed_streams
        except Exception as e:
            logger.error(f"Excel文件处理失败: {str(e)}")
            raise HTTPException(status_code=400, detail=f"Excel文件处理失败: {str(e)}")
    
    def _merge_results(self, results: List[dict]) -> Dict[str, Any]:
        """合并多个处理结果"""
        if not results:
            return {"工序": []}
        
        processes = []
        merged = {"工序": []}
        
        for result in results:
            if isinstance(result, dict):
                # 提取工序信息
                if "工序" in result:
                    processes.extend(result["工序"])
                
                # 合并其他字段
                for key, value in result.items():
                    if key != "工序" and key not in merged:
                        merged[key] = value
        
        merged["工序"] = processes
        return merged
    
    async def process_upload(self, file: UploadFile, max_len: int) -> Dict[str, Any]:
        """处理SOP文件上传"""
        # 验证max_len参数
        if max_len <= 0 or max_len > 1000:
            raise HTTPException(status_code=400, detail="max_len必须在1-1000之间")
        
        # 验证文件
        self._validate_file(file)
        
        # 读取文件内容
        file_bytes = await file.read()
        if not file_bytes:
            raise HTTPException(status_code=400, detail="文件内容为空")
        
        # 处理文件流
        file_streams = self._process_file_streams(file_bytes, max_len)
        
        if not file_streams:
            raise HTTPException(status_code=400, detail="文件处理后没有生成有效的数据块")
        
        # 批量处理文件
        workflow_results = await self.dify_service.process_files_batch(file_streams)
        
        if not workflow_results:
            raise HTTPException(status_code=500, detail="所有文件处理失败，请检查文件格式和内容")
        
        # 提取成功的结果数据
        successful_results = []
        for result in workflow_results:
            if result.status == 1 and result.data:
                successful_results.append(result.data)
        
        if not successful_results:
            raise HTTPException(status_code=500, detail="工作流处理成功但没有返回有效数据")
        
        # 合并结果并直接返回
        return self._merge_results(successful_results)
