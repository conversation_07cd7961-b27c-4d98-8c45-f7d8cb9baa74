import asyncio
import logging
from typing import List, <PERSON>ple

import aiohttp

from fastapi import HTTPException

from app.core.config import settings
from app.schemas.sop import DifyWorkflowResult, FileUploadResult
from app.utils.json_parser import JSONParser

logger = logging.getLogger(__name__)


class DifyService:
    def __init__(self):
        self.json_parser = JSONParser()
        self.base_url = settings.DIFY_API_URL
        self.api_key = settings.DIFY_API_KEY
        self.user = settings.DIFY_USER

    def _get_headers(self) -> dict:
        return {
            "Authorization": f"Bearer {self.api_key}",
        }

    async def upload_file(self, filename: str, file_bytes: bytes) -> FileUploadResult:
        upload_url = f"{self.base_url}/v1/files/upload"

        data = aiohttp.FormData()
        data.add_field(
            "file",
            file_bytes,
            filename=filename,
            content_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
        )
        data.add_field("user", self.user)

        async with aiohttp.ClientSession() as session:
            async with session.post(
                upload_url, headers=self._get_headers(), data=data
            ) as response:
                if response.status != 201:
                    error_text = await response.text()
                    logger.error(f"Dify文件上传失败: {error_text}")
                    raise DifyServiceException(
                        message=f"文件上传失败: {error_text}",
                        details={"status_code": response.status, "filename": filename},
                    )

                result = await response.json()
                return FileUploadResult(
                    file_id=result.get("id"), filename=filename, size=len(file_bytes)
                )

    async def run_workflow(self, file_id: str) -> DifyWorkflowResult:
        workflow_url = f"{self.base_url}/v1/workflows/run"

        payload = {
            "inputs": {
                "planning_file": {
                    "type": "document",
                    "transfer_method": "local_file",
                    "upload_file_id": file_id,
                }
            },
            "response_mode": "blocking",
            "user": self.user,
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                workflow_url, headers=self._get_headers(), json=payload
            ) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"Dify工作流调用失败: {error_text}")
                    raise DifyServiceException(
                        message=f"工作流调用失败: {error_text}",
                        details={"status_code": response.status, "file_id": file_id},
                    )

                result = await response.json()

                try:
                    outputs = result.get("data", {}).get("outputs", {})
                    text_output = outputs.get("text", "")

                    if text_output:
                        parsed_data = self.json_parser.parse(text_output)
                        return DifyWorkflowResult(
                            status=1, data=parsed_data, message="工作流执行成功"
                        )
                    else:
                        return DifyWorkflowResult(
                            status=0, data=None, message="工作流返回空结果"
                        )

                except Exception as e:
                    return DifyWorkflowResult(
                        status=0, data=None, message=f"解析工作流结果失败: {str(e)}"
                    )

    async def process_files_batch(
        self, file_streams: List[Tuple[str, bytes]]
    ) -> List[DifyWorkflowResult]:
        """批量处理文件"""
        results = []

        upload_tasks = [
            self.upload_file(filename, stream_bytes)
            for filename, stream_bytes in file_streams
        ]

        try:
            upload_results = await asyncio.gather(*upload_tasks, return_exceptions=True)

            valid_uploads = []
            for result in upload_results:
                if isinstance(result, Exception):
                    continue
                valid_uploads.append(result)

            if not valid_uploads:
                logger.error("所有文件上传失败")
                raise DifyServiceException(
                    message="所有文件上传失败",
                    details={"total_files": len(file_streams)},
                )

            workflow_tasks = [
                self.run_workflow(upload_result.file_id)
                for upload_result in valid_uploads
            ]

            workflow_results = await asyncio.gather(
                *workflow_tasks, return_exceptions=True
            )

            for result in workflow_results:
                if isinstance(result, Exception):
                    continue
                if result.status == 1:
                    results.append(result)

            return results

        except Exception as e:
            logger.error(f"批量处理文件失败: {str(e)}")
            raise DifyServiceException(
                message=f"批量处理文件失败: {str(e)}",
                details={"total_files": len(file_streams)},
            )
