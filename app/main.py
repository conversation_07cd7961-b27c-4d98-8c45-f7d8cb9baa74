import logging
import time
from contextlib import asynccontextmanager

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from app.api.v1.routers import api_router
from app.core.config import settings
from app.core.exception_handlers import (
    base_api_exception_handler,
    general_exception_handler,
    http_exception_handler,
    validation_exception_handler,
)
from app.core.exceptions import BaseAPIException
from app.core.log import setup_logging
from app.middleware.log import LoggingMiddleware
from app.middleware.request_id import RequestIdMiddleware
from app.schemas.sop import HealthCheckResponse


@asynccontextmanager
async def lifespan(app: FastAPI):
    setup_logging()
    logging.info(f"服务正在启动... 环境: {settings.ENVIRONMENT}, 版本: {settings.VERSION}")

    yield

    logging.info("服务已关闭")


def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    app = FastAPI(
        title="AI Server",
        description="基于FastAPI的AI服务后端项目",
        version=settings.VERSION,
        debug=settings.DEBUG,
        lifespan=lifespan
    )

    # 添加中间件
    app.add_middleware(LoggingMiddleware)
    app.add_middleware(RequestIdMiddleware)

    # 添加异常处理器
    app.add_exception_handler(BaseAPIException, base_api_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(ValidationError, validation_exception_handler)
    app.add_exception_handler(Exception, general_exception_handler)

    # 添加路由
    app.include_router(api_router, prefix="/api/v1")

    return app


app = create_app()


@app.get("/", include_in_schema=False)
def read_root():
    """根路径重定向"""
    return {"message": "AI Server API", "docs": "/docs", "health": "/health"}


@app.get("/health", response_model=HealthCheckResponse, tags=["系统"])
async def health_check():
    """健康检查端点"""
    return HealthCheckResponse(
        status="healthy",
        timestamp=time.time(),
        version=settings.VERSION,
        environment=settings.ENVIRONMENT
    )


if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
