import logging
import time
from contextlib import asynccontextmanager

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>

from app.api.v1.routers import api_router
from app.core.log import setup_logging
from app.middleware.log import LoggingMiddleware
from app.middleware.request_id import RequestIdMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    setup_logging()
    logging.info("服务正在启动...")

    yield

    logging.info("服务已关闭")


app = FastAPI(lifespan=lifespan)
app.add_middleware(LoggingMiddleware)
app.add_middleware(RequestIdMiddleware)
app.include_router(api_router)


@app.get("/")
def read_root():
    return {"Hello": "World"}


@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {"status": "healthy", "timestamp": time.time()}


if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=30101, reload=True)
