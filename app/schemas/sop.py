from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator


class SOPUploadRequest(BaseModel):
    max_len: int = Field(default=40, ge=1, le=1000, description="每个分块的最大行数")

    @field_validator("max_len")
    def validate_max_len(cls, v):
        if v <= 0:
            raise ValueError("max_len必须大于0")
        return v


class ProcessStep(BaseModel):
    """工序步骤模型"""

    step_id: Optional[str] = Field(None, description="步骤ID")
    step_name: Optional[str] = Field(None, description="步骤名称")
    description: Optional[str] = Field(None, description="步骤描述")
    duration: Optional[str] = Field(None, description="持续时间")
    resources: Optional[str] = Field(None, description="所需资源")


class SOPResult(BaseModel):
    """单个SOP处理结果模型"""

    工序: List[ProcessStep] = Field(default_factory=list, description="工序列表")

    class Config:
        # 允许额外字段，因为结果可能包含动态字段
        extra = "allow"


class SOPUploadResponse(BaseModel):
    """SOP上传响应模型"""

    success: bool = Field(description="处理是否成功")
    data: Optional[SOPResult] = Field(None, description="处理结果数据")
    message: Optional[str] = Field(None, description="响应消息")

    # 允许额外字段以保持向后兼容性
    class Config:
        extra = "allow"


class ErrorResponse(BaseModel):
    """错误响应模型"""

    success: bool = Field(default=False, description="处理是否成功")
    error: str = Field(description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
    details: Optional[Dict[str, Any]] = Field(None, description="错误详情")


class HealthCheckResponse(BaseModel):
    """健康检查响应模型"""

    status: str = Field(description="服务状态")
    timestamp: float = Field(description="检查时间戳")
    version: Optional[str] = Field(None, description="服务版本")
    environment: Optional[str] = Field(None, description="运行环境")


class DifyWorkflowResult(BaseModel):
    """Dify工作流结果模型"""

    status: int = Field(description="状态码")
    data: Optional[Dict[str, Any]] = Field(None, description="结果数据")
    message: Optional[str] = Field(None, description="消息")


class FileUploadResult(BaseModel):
    """文件上传结果模型"""

    file_id: str = Field(description="上传后的文件ID")
    filename: str = Field(description="文件名")
    size: Optional[int] = Field(None, description="文件大小")
