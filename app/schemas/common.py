"""通用数据模型和验证schemas"""

from typing import Any, Dict, Optional

from pydantic import BaseModel, Field


class BaseResponse(BaseModel):
    """基础响应模型"""

    success: bool = Field(description="操作是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")


class ErrorDetail(BaseModel):
    """错误详情模型"""

    field: Optional[str] = Field(None, description="错误字段")
    message: str = Field(description="错误消息")
    code: Optional[str] = Field(None, description="错误代码")


class ValidationErrorResponse(BaseModel):
    """验证错误响应模型"""

    success: bool = Field(default=False, description="操作是否成功")
    message: str = Field(description="错误消息")
    errors: list[ErrorDetail] = Field(description="详细错误列表")


class PaginationParams(BaseModel):
    """分页参数模型"""

    page: int = Field(default=1, ge=1, description="页码")
    size: int = Field(default=20, ge=1, le=100, description="每页大小")


class PaginatedResponse(BaseModel):
    """分页响应模型"""

    success: bool = Field(description="操作是否成功")
    data: list[Any] = Field(description="数据列表")
    total: int = Field(description="总数量")
    page: int = Field(description="当前页码")
    size: int = Field(description="每页大小")
    pages: int = Field(description="总页数")

    @property
    def has_next(self) -> bool:
        """是否有下一页"""
        return self.page < self.pages

    @property
    def has_prev(self) -> bool:
        """是否有上一页"""
        return self.page > 1
