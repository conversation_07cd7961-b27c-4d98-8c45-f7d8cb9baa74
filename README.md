# AI Server

基于FastAPI的AI服务后端项目。

## 功能特性

- FastAPI框架
- Poetry依赖管理
- 自动化测试
- 日志中间件
- CI/CD支持

## 环境要求

- Python 3.10+
- Poetry

## 安装

1. 克隆项目
```bash
git clone [项目地址]
cd ai-server
```

2. 安装依赖
```bash
make install
```

## 使用

1. 启动服务
```bash
make run
```

2. 格式化代码
```bash
make format
```

3. 运行测试
```bash
make test
```

4. 清理缓存
```bash
make clean
```

## 开发指南

### 项目结构
```
ai-server/
├── app/
│   ├── api/        # API路由
│   ├── core/       # 核心配置
│   ├── middleware/ # 中间件
│   └── utils/      # 工具函数
├── tests/          # 测试文件
├── Makefile        # 项目命令
└── pyproject.toml  # 项目配置
```

### 开发流程

1. 创建新分支
2. 开发新功能
3. 运行测试
4. 提交代码
5. 创建合并请求

## API文档

启动服务后访问：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc
